<?php

namespace App\Http\Controllers;

use App\Models\Dealer;
use App\Models\Region;
use Illuminate\Http\Request;
use App\Http\Requests\StoreDealerRequest;
use App\Http\Requests\UpdateDealerRequest;

class DealerController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $perPage = request('perPage', 10);
        $dealers = Dealer::with(['region', 'customers'])
            ->search(request('q'))
            ->orderBy('id', 'desc')
            ->paginate($perPage)
            ->appends(request()->query());

        return view('dealers.index', compact('dealers'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $regions = Region::active()->orderBy('name')->get();
        return view('dealers.create', compact('regions'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreDealerRequest $request)
    {
        $validated = $request->validated();
        $validated['status'] = $request->has('status');

        Dealer::create($validated);

        return redirect()->route('dealers.index')->with('success', 'Bayi başarıyla eklendi!');
    }

    /**
     * Display the specified resource.
     */
    public function show(Dealer $dealer)
    {
        $dealer->load(['region', 'customers']);
        return view('dealers.show', compact('dealer'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Dealer $dealer)
    {
        $regions = Region::active()->orderBy('name')->get();
        return view('dealers.edit', compact('dealer', 'regions'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateDealerRequest $request, Dealer $dealer)
    {
        $validated = $request->validated();
        $validated['status'] = $request->has('status');

        $dealer->update($validated);

        return redirect()->route('dealers.index')->with('success', 'Bayi başarıyla güncellendi!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Dealer $dealer)
    {
        // Bayiye bağlı müşteri varsa silme
        if ($dealer->customers()->count() > 0) {
            return redirect()->route('dealers.index')->with('error', 'Bu bayiye bağlı müşteriler bulunduğu için silinemez!');
        }

        $dealer->delete();

        return redirect()->route('dealers.index')->with('success', 'Bayi başarıyla silindi!');
    }
}
