<?php

namespace App\Http\Controllers;

use App\Models\Region;
use Illuminate\Http\Request;
use App\Http\Requests\StoreRegionRequest;
use App\Http\Requests\UpdateRegionRequest;

class RegionController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        // DataTable kullanıyoruz, bu yüzden sadece view döndürüyoruz
        return view('regions.index');
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('regions.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreRegionRequest $request)
    {
        $validated = $request->validated();
        $validated['status'] = $request->has('status');

        Region::create($validated);

        return redirect()->route('regions.index')->with('success', 'Bölge başarıyla eklendi!');
    }

    /**
     * Display the specified resource.
     */
    public function show(Region $region)
    {
        $region->load('dealers');
        return view('regions.show', compact('region'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Region $region)
    {
        return view('regions.edit', compact('region'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateRegionRequest $request, Region $region)
    {
        $validated = $request->validated();
        $validated['status'] = $request->has('status');

        $region->update($validated);

        return redirect()->route('regions.index')->with('success', 'Bölge başarıyla güncellendi!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Region $region)
    {
        // Bölgeye bağlı bayi varsa silme
        if ($region->dealers()->count() > 0) {
            return redirect()->route('regions.index')->with('error', 'Bu bölgeye bağlı bayiler bulunduğu için silinemez!');
        }

        $region->delete();

        return redirect()->route('regions.index')->with('success', 'Bölge başarıyla silindi!');
    }

    /**
     * DataTable AJAX endpoint for regions
     */
    public function datatable(Request $request)
    {
        $query = Region::with('dealers');

        // Global search
        if ($request->has('search') && $request->search['value']) {
            $searchValue = $request->search['value'];
            $query->where(function ($q) use ($searchValue) {
                $q->where('name', 'ILIKE', "%{$searchValue}%")
                  ->orWhere('description', 'ILIKE', "%{$searchValue}%");
            });
        }

        // Column-specific search
        if ($request->has('columns')) {
            foreach ($request->columns as $index => $column) {
                if (isset($column['search']['value']) && $column['search']['value'] !== '') {
                    $searchValue = $column['search']['value'];

                    switch ($index) {
                        case 0: // ID
                            $query->where('id', $searchValue);
                            break;
                        case 1: // Name
                            $query->where('name', 'ILIKE', "%{$searchValue}%");
                            break;
                        case 2: // Description
                            $query->where('description', 'ILIKE', "%{$searchValue}%");
                            break;
                        case 3: // Dealers Count
                            $query->has('dealers', '>=', (int)$searchValue);
                            break;
                        case 4: // Status
                            if (strtolower($searchValue) === 'aktif' || $searchValue === '1') {
                                $query->where('status', true);
                            } elseif (strtolower($searchValue) === 'pasif' || $searchValue === '0') {
                                $query->where('status', false);
                            }
                            break;
                    }
                }
            }
        }

        // Ordering
        if ($request->has('order')) {
            $orderColumn = $request->order[0]['column'];
            $orderDir = $request->order[0]['dir'];

            switch ($orderColumn) {
                case 0:
                    $query->orderBy('id', $orderDir);
                    break;
                case 1:
                    $query->orderBy('name', $orderDir);
                    break;
                case 2:
                    $query->orderBy('description', $orderDir);
                    break;
                case 4:
                    $query->orderBy('status', $orderDir);
                    break;
                case 5:
                    $query->orderBy('created_at', $orderDir);
                    break;
                default:
                    $query->orderBy('id', 'desc');
                    break;
            }
        } else {
            $query->orderBy('id', 'desc');
        }

        // Get total count before pagination
        $totalRecords = Region::count();
        $filteredRecords = $query->count();

        // Pagination
        $start = $request->start ?? 0;
        $length = $request->length ?? 10;
        $regions = $query->skip($start)->take($length)->get();

        $data = [];
        foreach ($regions as $region) {
            $statusBadge = $region->status
                ? '<span class="badge badge-success">Aktif</span>'
                : '<span class="badge badge-secondary">Pasif</span>';

            $actions = '
                <div class="btn-group" role="group">
                    <a href="' . route('regions.show', $region->id) . '" class="btn btn-sm btn-info" title="Görüntüle">
                        <i class="fas fa-eye"></i>
                    </a>
                    <a href="' . route('regions.edit', $region->id) . '" class="btn btn-sm btn-warning" title="Düzenle">
                        <i class="fas fa-edit"></i>
                    </a>
                    <form action="' . route('regions.destroy', $region->id) . '" method="POST" style="display: inline;"
                          onsubmit="return confirm(\'Bu bölgeyi silmek istediğinizden emin misiniz?\')">
                        ' . csrf_field() . '
                        ' . method_field('DELETE') . '
                        <button type="submit" class="btn btn-sm btn-danger" title="Sil">
                            <i class="fas fa-trash"></i>
                        </button>
                    </form>
                </div>';

            $data[] = [
                $region->id,
                $region->name,
                $region->description ?? '-',
                $region->dealers->count(),
                $statusBadge,
                $region->created_at->format('d.m.Y H:i'),
                $actions
            ];
        }

        return response()->json([
            'draw' => intval($request->draw),
            'recordsTotal' => $totalRecords,
            'recordsFiltered' => $filteredRecords,
            'data' => $data
        ]);
    }
}
