<?php

namespace App\Http\Controllers;

use App\Models\Region;
use Illuminate\Http\Request;
use App\Http\Requests\StoreRegionRequest;
use App\Http\Requests\UpdateRegionRequest;

class RegionController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $perPage = request('perPage', 10);
        $regions = Region::with('dealers')
            ->search(request('q'))
            ->orderBy('id', 'desc')
            ->paginate($perPage)
            ->appends(request()->query());

        return view('regions.index', compact('regions'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('regions.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreRegionRequest $request)
    {
        $validated = $request->validated();
        $validated['status'] = $request->has('status');

        Region::create($validated);

        return redirect()->route('regions.index')->with('success', '<PERSON><PERSON><PERSON> başarıyla eklendi!');
    }

    /**
     * Display the specified resource.
     */
    public function show(Region $region)
    {
        $region->load('dealers');
        return view('regions.show', compact('region'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Region $region)
    {
        return view('regions.edit', compact('region'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateRegionRequest $request, Region $region)
    {
        $validated = $request->validated();
        $validated['status'] = $request->has('status');

        $region->update($validated);

        return redirect()->route('regions.index')->with('success', 'Bölge başarıyla güncellendi!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Region $region)
    {
        // Bölgeye bağlı bayi varsa silme
        if ($region->dealers()->count() > 0) {
            return redirect()->route('regions.index')->with('error', 'Bu bölgeye bağlı bayiler bulunduğu için silinemez!');
        }

        $region->delete();

        return redirect()->route('regions.index')->with('success', 'Bölge başarıyla silindi!');
    }
}
