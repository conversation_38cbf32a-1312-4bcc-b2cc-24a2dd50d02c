<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreDealerRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'region_id' => 'required|exists:regions,id',
            'name' => 'required|string|max:255',
            'contact_person' => 'nullable|string|max:255',
            'phone' => 'nullable|string|max:20',
            'email' => 'nullable|email|max:255',
            'address' => 'nullable|string|max:1000',
            'city' => 'nullable|string|max:100',
            'district' => 'nullable|string|max:100',
            'status' => 'boolean',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'region_id.required' => 'Bölge seçimi zorunludur.',
            'region_id.exists' => 'Seçilen bölge geçerli değil.',
            'name.required' => 'Bayi adı zorunludur.',
            'name.max' => 'Bayi adı en fazla 255 karakter olabilir.',
            'contact_person.max' => 'İletişim kişisi en fazla 255 karakter olabilir.',
            'phone.max' => 'Telefon en fazla 20 karakter olabilir.',
            'email.email' => 'Geçerli bir e-posta adresi giriniz.',
            'email.max' => 'E-posta en fazla 255 karakter olabilir.',
            'address.max' => 'Adres en fazla 1000 karakter olabilir.',
            'city.max' => 'Şehir en fazla 100 karakter olabilir.',
            'district.max' => 'İlçe en fazla 100 karakter olabilir.',
        ];
    }
}
