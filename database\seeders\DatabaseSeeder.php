<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // \App\Models\User::factory(10)->create();
        $this->call(RoleSeeder::class);
        $this->call(CustomerSeeder::class);
        $this->call(CustomerFollowupSeeder::class);
        $this->call(PotentialCustomerSeeder::class);
        $this->call(RegionSeeder::class);
        $this->call(DealerSeeder::class);
        
        DB::table('users')->truncate();

        User::factory()->create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'phone' => '+90 ************',
            'password' => Hash::make('test1234'),
        ]);
    }
}
