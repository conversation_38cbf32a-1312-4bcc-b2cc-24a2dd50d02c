<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Region;
use App\Models\Dealer;

class DealerSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Önce bölgeleri al
        $marmara = Region::where('name', 'Marmara Bölgesi')->first();
        $ege = Region::where('name', 'Ege Bölgesi')->first();
        $akdeniz = Region::where('name', 'Akdeniz Bölgesi')->first();
        $icAnadolu = Region::where('name', 'İç Anadolu Bölgesi')->first();
        $karadeniz = Region::where('name', 'Karadeniz Bölgesi')->first();

        $dealers = [
            // Marmara Bölgesi Bayileri
            [
                'region_id' => $marmara->id,
                'name' => 'İstanbul Merkez Bayi',
                'contact_person' => 'Ahmet Yılmaz',
                'phone' => '+90 ************',
                'email' => '<EMAIL>',
                'address' => 'Atatürk Mahallesi, İş Merkezi No:15',
                'city' => 'İstanbul',
                'district' => 'Şişli',
                'status' => true,
            ],
            [
                'region_id' => $marmara->id,
                'name' => 'Bursa Bayi',
                'contact_person' => 'Mehmet Kaya',
                'phone' => '+90 ************',
                'email' => '<EMAIL>',
                'address' => 'Cumhuriyet Caddesi No:45',
                'city' => 'Bursa',
                'district' => 'Osmangazi',
                'status' => true,
            ],
            // Ege Bölgesi Bayileri
            [
                'region_id' => $ege->id,
                'name' => 'İzmir Alsancak Bayi',
                'contact_person' => 'Ayşe Demir',
                'phone' => '+90 ************',
                'email' => '<EMAIL>',
                'address' => 'Alsancak Mahallesi, Kordon Boyu No:78',
                'city' => 'İzmir',
                'district' => 'Konak',
                'status' => true,
            ],
            [
                'region_id' => $ege->id,
                'name' => 'Manisa Bayi',
                'contact_person' => 'Fatma Özkan',
                'phone' => '+90 ************',
                'email' => '<EMAIL>',
                'address' => 'Merkez Mahallesi No:23',
                'city' => 'Manisa',
                'district' => 'Şehzadeler',
                'status' => true,
            ],
            // Akdeniz Bölgesi Bayileri
            [
                'region_id' => $akdeniz->id,
                'name' => 'Antalya Konyaaltı Bayi',
                'contact_person' => 'Murat Çelik',
                'phone' => '+90 ************',
                'email' => '<EMAIL>',
                'address' => 'Konyaaltı Bulvarı No:156',
                'city' => 'Antalya',
                'district' => 'Konyaaltı',
                'status' => true,
            ],
            // İç Anadolu Bölgesi Bayileri
            [
                'region_id' => $icAnadolu->id,
                'name' => 'Ankara Kızılay Bayi',
                'contact_person' => 'Selim Aydın',
                'phone' => '+90 ************',
                'email' => '<EMAIL>',
                'address' => 'Kızılay Meydanı No:34',
                'city' => 'Ankara',
                'district' => 'Çankaya',
                'status' => true,
            ],
            [
                'region_id' => $icAnadolu->id,
                'name' => 'Konya Bayi',
                'contact_person' => 'Zeynep Yıldız',
                'phone' => '+90 ************',
                'email' => '<EMAIL>',
                'address' => 'Meram Caddesi No:89',
                'city' => 'Konya',
                'district' => 'Meram',
                'status' => true,
            ],
            // Karadeniz Bölgesi Bayileri
            [
                'region_id' => $karadeniz->id,
                'name' => 'Samsun Bayi',
                'contact_person' => 'Hasan Polat',
                'phone' => '+90 ************',
                'email' => '<EMAIL>',
                'address' => 'Atatürk Bulvarı No:67',
                'city' => 'Samsun',
                'district' => 'İlkadım',
                'status' => true,
            ],
        ];

        foreach ($dealers as $dealer) {
            Dealer::create($dealer);
        }
    }
}
