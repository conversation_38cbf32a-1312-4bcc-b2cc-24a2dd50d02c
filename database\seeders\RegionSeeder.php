<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Region;

class RegionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $regions = [
            [
                'name' => 'Marmara Bölgesi',
                'description' => 'İstanbul, Bursa, Kocaeli ve çevre illeri kapsayan bölge',
                'status' => true,
            ],
            [
                'name' => 'Ege Bölgesi',
                'description' => 'İzmir, Manisa, Aydın ve çevre illeri kapsayan bölge',
                'status' => true,
            ],
            [
                'name' => 'Ak<PERSON>iz Bölgesi',
                'description' => '<PERSON><PERSON><PERSON>, Mersin, Adana ve çevre illeri kapsayan bölge',
                'status' => true,
            ],
            [
                'name' => 'İç Anadolu Bölgesi',
                'description' => 'Ankara, Konya, Kayseri ve çevre illeri kapsayan bölge',
                'status' => true,
            ],
            [
                'name' => 'Karadeniz Bölgesi',
                'description' => '<PERSON><PERSON>, Trab<PERSON>, Ordu ve çevre illeri kapsayan bölge',
                'status' => true,
            ],
            [
                'name' => 'Doğu Anadolu Bölgesi',
                'description' => 'Erzurum, Van, Malatya ve çevre illeri kapsayan bölge',
                'status' => true,
            ],
            [
                'name' => 'Güneydoğu Anadolu Bölgesi',
                'description' => 'Gaziantep, Şanlıurfa, Diyarbakır ve çevre illeri kapsayan bölge',
                'status' => true,
            ],
        ];

        foreach ($regions as $region) {
            Region::create($region);
        }
    }
}
