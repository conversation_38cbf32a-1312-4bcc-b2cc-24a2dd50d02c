@extends('layouts.index')

@section('title', 'Bayi <PERSON>netimi')

@section('content')
<!-- Content Header (Page header) -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">Bayi Yönetimi</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Ana Sayfa</a></li>
                    <li class="breadcrumb-item active">Bayi Yönetimi</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <a href="{{ route('dealers.create') }}" class="btn btn-primary">Yeni <PERSON><PERSON></a>
                        </h3>

                        <div class="card-tools">
                            @if(session('success'))
                                <div class="alert alert-success">
                                    {{ session('success') }}
                                </div>
                            @endif

                            @if(session('error'))
                                <div class="alert alert-danger">
                                    {{ session('error') }}
                                </div>
                            @endif

                            <form method="GET" action="" class="d-flex">
                                <div class="input-group input-group-sm">
                                    <input type="text" name="q" class="form-control float-right" placeholder="Bayi adı, şehir ara..." value="{{ request('q') }}">
                                    <div class="input-group-append">
                                        <button type="submit" class="btn btn-default">
                                            <i class="fas fa-search"></i>
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                    <!-- /.card-header -->
                    <div class="card-body table-responsive p-0">
                        <table class="table table-hover text-nowrap">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Bayi Adı</th>
                                    <th>Bölge</th>
                                    <th>İletişim Kişisi</th>
                                    <th>Telefon</th>
                                    <th>Şehir/İlçe</th>
                                    <th>Müşteri Sayısı</th>
                                    <th>Durum</th>
                                    <th>İşlemler</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($dealers as $dealer)
                                    <tr>
                                        <td>{{ $dealer->id }}</td>
                                        <td>{{ $dealer->name }}</td>
                                        <td>{{ $dealer->region->name ?? '-' }}</td>
                                        <td>{{ $dealer->contact_person ?? '-' }}</td>
                                        <td>{{ $dealer->phone ?? '-' }}</td>
                                        <td>{{ $dealer->city }} / {{ $dealer->district }}</td>
                                        <td>{{ $dealer->customers_count ?? $dealer->customers->count() }}</td>
                                        <td>
                                            @if($dealer->status)
                                                <span class="badge badge-success">Aktif</span>
                                            @else
                                                <span class="badge badge-danger">Pasif</span>
                                            @endif
                                        </td>
                                        <td>
                                            <a href="{{ route('dealers.show', $dealer->id) }}" class="btn btn-info btn-sm" title="Detay">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ route('dealers.edit', $dealer->id) }}" class="btn btn-warning btn-sm" title="Düzenle">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <form action="{{ route('dealers.destroy', $dealer->id) }}" method="POST" style="display: inline-block;" onsubmit="return confirm('Bu bayiyi silmek istediğinizden emin misiniz?')">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="btn btn-danger btn-sm" title="Sil">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="9" class="text-center">Henüz bayi eklenmemiş.</td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                    <!-- /.card-body -->
                    <div class="card-footer clearfix">
                        {{ $dealers->links() }}
                    </div>
                </div>
                <!-- /.card -->
            </div>
        </div>
    </div>
</section>
@endsection
