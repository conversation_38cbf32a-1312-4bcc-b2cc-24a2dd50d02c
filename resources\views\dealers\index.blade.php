@extends('layouts.index')

@section('title', 'Bayi <PERSON>önetimi')

@section('content')
<!-- Content Header (Page header) -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">Bayi Yönetimi</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Ana Sayfa</a></li>
                    <li class="breadcrumb-item active">Bayi Yönetimi</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Bayiler</h3>
                        <div class="card-tools">
                            <a href="{{ route('dealers.create') }}" class="btn btn-primary btn-sm">
                                <i class="fas fa-plus"></i> Yeni Bayi Ekle
                            </a>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table id="dealersTable" class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Bayi Adı</th>
                                        <th>Bölge</th>
                                        <th>İletişim Kişisi</th>
                                        <th>Telefon</th>
                                        <th>E-posta</th>
                                        <th>Şehir</th>
                                        <th>İlçe</th>
                                        <th>Müşteri Sayısı</th>
                                        <th>Durum</th>
                                        <th>Oluşturulma Tarihi</th>
                                        <th>İşlemler</th>
                                    </tr>
                                </thead>
                                <tfoot>
                                    <tr>
                                        <th><input type="text" class="form-control form-control-sm" placeholder="ID Ara"></th>
                                        <th><input type="text" class="form-control form-control-sm" placeholder="Bayi Adı Ara"></th>
                                        <th><input type="text" class="form-control form-control-sm" placeholder="Bölge Ara"></th>
                                        <th><input type="text" class="form-control form-control-sm" placeholder="İletişim Kişisi"></th>
                                        <th><input type="text" class="form-control form-control-sm" placeholder="Telefon Ara"></th>
                                        <th><input type="text" class="form-control form-control-sm" placeholder="E-posta Ara"></th>
                                        <th><input type="text" class="form-control form-control-sm" placeholder="Şehir Ara"></th>
                                        <th><input type="text" class="form-control form-control-sm" placeholder="İlçe Ara"></th>
                                        <th><input type="text" class="form-control form-control-sm" placeholder="Müşteri Sayısı"></th>
                                        <th>
                                            <select class="form-control form-control-sm">
                                                <option value="">Tümü</option>
                                                <option value="aktif">Aktif</option>
                                                <option value="pasif">Pasif</option>
                                            </select>
                                        </th>
                                        <th></th>
                                        <th></th>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection

@push('styles')
<!-- DataTables -->
<link rel="stylesheet" href="{{ asset('plugins/datatables-bs4/css/dataTables.bootstrap4.min.css') }}">
<link rel="stylesheet" href="{{ asset('plugins/datatables-responsive/css/responsive.bootstrap4.min.css') }}">
<link rel="stylesheet" href="{{ asset('plugins/datatables-buttons/css/buttons.bootstrap4.min.css') }}">
@endpush

@push('scripts')
<!-- DataTables  & Plugins -->
<script src="{{ asset('plugins/datatables/jquery.dataTables.min.js') }}"></script>
<script src="{{ asset('plugins/datatables-bs4/js/dataTables.bootstrap4.min.js') }}"></script>
<script src="{{ asset('plugins/datatables-responsive/js/dataTables.responsive.min.js') }}"></script>
<script src="{{ asset('plugins/datatables-responsive/js/responsive.bootstrap4.min.js') }}"></script>
<script src="{{ asset('plugins/datatables-buttons/js/dataTables.buttons.min.js') }}"></script>
<script src="{{ asset('plugins/datatables-buttons/js/buttons.bootstrap4.min.js') }}"></script>

<script>
$(document).ready(function() {
    var table = $('#dealersTable').DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: '{{ route("dealers.datatable") }}',
            type: 'GET'
        },
        columns: [
            { data: 0, name: 'id', searchable: true },
            { data: 1, name: 'name', searchable: true },
            { data: 2, name: 'region.name', searchable: true },
            { data: 3, name: 'contact_person', searchable: true },
            { data: 4, name: 'phone', searchable: true },
            { data: 5, name: 'email', searchable: true },
            { data: 6, name: 'city', searchable: true },
            { data: 7, name: 'district', searchable: true },
            { data: 8, name: 'customers_count', searchable: true, orderable: false },
            { data: 9, name: 'status', searchable: true },
            { data: 10, name: 'created_at', searchable: false },
            { data: 11, name: 'actions', searchable: false, orderable: false }
        ],
        order: [[0, 'desc']],
        pageLength: 25,
        lengthMenu: [[10, 25, 50, 100, -1], [10, 25, 50, 100, "Tümü"]],
        language: {
            url: '//cdn.datatables.net/plug-ins/1.13.7/i18n/tr.json'
        },
        responsive: true,
        autoWidth: false,
        scrollX: true,
        initComplete: function () {
            // Apply the search
            this.api().columns().every(function (index) {
                var that = this;
                var input = $('tfoot th').eq(index).find('input, select');

                if (input.length) {
                    input.on('keyup change clear', function () {
                        if (that.search() !== this.value) {
                            that.search(this.value).draw();
                        }
                    });
                }
            });
        }
    });
});
</script>
@endpush
