@extends('layouts.index')

@section('title', '<PERSON><PERSON><PERSON>')

@section('content')
<!-- Content Header (Page header) -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0"><PERSON><PERSON><PERSON>im<PERSON></h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Ana Sayfa</a></li>
                    <li class="breadcrumb-item active"><PERSON><PERSON><PERSON> Yönetimi</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <a href="{{ route('regions.create') }}" class="btn btn-primary"><PERSON><PERSON></a>
                        </h3>

                        <div class="card-tools">
                            @if(session('success'))
                                <div class="alert alert-success">
                                    {{ session('success') }}
                                </div>
                            @endif

                            @if(session('error'))
                                <div class="alert alert-danger">
                                    {{ session('error') }}
                                </div>
                            @endif

                            <form method="GET" action="" class="d-flex">
                                <div class="input-group input-group-sm">
                                    <input type="text" name="q" class="form-control float-right" placeholder="Bölge adı ara..." value="{{ request('q') }}">
                                    <div class="input-group-append">
                                        <button type="submit" class="btn btn-default">
                                            <i class="fas fa-search"></i>
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                    <!-- /.card-header -->
                    <div class="card-body table-responsive p-0">
                        <table class="table table-hover text-nowrap">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Bölge Adı</th>
                                    <th>Açıklama</th>
                                    <th>Bayi Sayısı</th>
                                    <th>Durum</th>
                                    <th>Oluşturulma Tarihi</th>
                                    <th>İşlemler</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($regions as $region)
                                    <tr>
                                        <td>{{ $region->id }}</td>
                                        <td>{{ $region->name }}</td>
                                        <td>{{ Str::limit($region->description, 50) ?? '-' }}</td>
                                        <td>{{ $region->dealers_count ?? $region->dealers->count() }}</td>
                                        <td>
                                            @if($region->status)
                                                <span class="badge badge-success">Aktif</span>
                                            @else
                                                <span class="badge badge-danger">Pasif</span>
                                            @endif
                                        </td>
                                        <td>{{ $region->created_at->format('d.m.Y') }}</td>
                                        <td>
                                            <a href="{{ route('regions.show', $region->id) }}" class="btn btn-info btn-sm" title="Detay">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ route('regions.edit', $region->id) }}" class="btn btn-warning btn-sm" title="Düzenle">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <form action="{{ route('regions.destroy', $region->id) }}" method="POST" style="display: inline-block;" onsubmit="return confirm('Bu bölgeyi silmek istediğinizden emin misiniz?')">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="btn btn-danger btn-sm" title="Sil">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="7" class="text-center">Henüz bölge eklenmemiş.</td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                    <!-- /.card-body -->
                    <div class="card-footer clearfix">
                        {{ $regions->links() }}
                    </div>
                </div>
                <!-- /.card -->
            </div>
        </div>
    </div>
</section>
@endsection
