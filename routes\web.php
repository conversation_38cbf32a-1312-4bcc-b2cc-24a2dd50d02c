<?php

use App\Http\Controllers\HomeController;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\CustomerController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\PotentialCustomerController;
use App\Http\Controllers\UserManagementController;
use App\Http\Controllers\RoleManagementController;

Route::middleware('auth')->group(function () {
    Route::get('/dashboard',[DashboardController::class,'index'])->name('dashboard');
    Route::get('/',[HomeController::class,'index'])->name('index');
    Route::get('/umrandev',[HomeController::class,'umrandev'])->name('umrandev');
    Route::get('/customers/index', [CustomerController::class, 'index'])->name('customers.index');
    Route::get('/customers/create', [CustomerController::class, 'create'])->name('customers.create');
    Route::post('/customers', [CustomerController::class, 'store'])->name('customers.store');
    Route::get('/customers/registrySearch', [CustomerController::class, 'registrySearch'])->name('customers.registrySearch');
    Route::get('/customers/{customer}', [CustomerController::class, 'show'])->name('customers.show');
    Route::get('/customers/{customer}/edit', [CustomerController::class, 'edit'])->name('customers.edit');
    Route::put('/customers/{customer}', [CustomerController::class, 'update'])->name('customers.update');
    Route::resource('potential-customers', PotentialCustomerController::class);
    Route::resource('customers.customer-followups', \App\Http\Controllers\CustomerFollowupController::class);
    Route::get('/customer-followups', [\App\Http\Controllers\CustomerFollowupController::class, 'allFollowups'])->name('customer-followups.all');
    Route::get('/customers/create-from-potential/{potentialCustomer}', [CustomerController::class, 'createFromPotential'])->name('customers.createFromPotential');

    // User Management Routes
    Route::get('user-management', [UserManagementController::class, 'index'])->name('user-management.index');
    Route::get('user-management/create', [UserManagementController::class, 'create'])->name('user-management.create');
    Route::post('user-management', [UserManagementController::class, 'store'])->name('user-management.store');
    Route::get('user-management/{user}', [UserManagementController::class, 'show'])->name('user-management.show');
    Route::get('user-management/{user}/edit', [UserManagementController::class, 'edit'])->name('user-management.edit');
    Route::put('user-management/{user}', [UserManagementController::class, 'update'])->name('user-management.update');
    Route::delete('user-management/{user}', [UserManagementController::class, 'destroy'])->name('user-management.destroy');

    // Role Management Routes
    Route::get('role-management', [RoleManagementController::class, 'index'])->name('role-management.index');
    Route::get('role-management/create', [RoleManagementController::class, 'create'])->name('role-management.create');
    Route::post('role-management', [RoleManagementController::class, 'store'])->name('role-management.store');
    Route::get('role-management/{role}', [RoleManagementController::class, 'show'])->name('role-management.show');
    Route::get('role-management/{role}/edit', [RoleManagementController::class, 'edit'])->name('role-management.edit');
    Route::put('role-management/{role}', [RoleManagementController::class, 'update'])->name('role-management.update');
    Route::delete('role-management/{role}', [RoleManagementController::class, 'destroy'])->name('role-management.destroy');
    Route::post('role-management/assign-role', [RoleManagementController::class, 'assignRole'])->name('role-management.assign-role');
    Route::delete('role-management/remove-role/{user}', [RoleManagementController::class, 'removeRole'])->name('role-management.remove-role');

    // DataTables AJAX endpoints
    Route::get('/api/customers/datatable', [CustomerController::class, 'datatable'])->name('customers.datatable');
    Route::get('/api/potential-customers/datatable', [PotentialCustomerController::class, 'datatable'])->name('potential-customers.datatable');
    Route::get('/api/customer-followups/all/datatable', [\App\Http\Controllers\CustomerFollowupController::class, 'allFollowupsDatatable'])->name('customer-followups.all.datatable');
    Route::get('/api/customers/{customer}/followups/datatable', [\App\Http\Controllers\CustomerFollowupController::class, 'datatable'])->name('customers.customer-followups.datatable');
});

Route::get('login', [AuthController::class, 'showLoginForm'])->name('login');
Route::post('login', [AuthController::class, 'login']);
Route::post('logout', [AuthController::class, 'logout'])->name('logout');