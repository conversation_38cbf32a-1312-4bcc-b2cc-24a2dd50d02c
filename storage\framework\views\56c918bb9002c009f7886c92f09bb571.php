<?php $__env->startSection('title', '<PERSON><PERSON>lge <PERSON>netimi'); ?>

<?php $__env->startSection('content'); ?>
<!-- Content Header (Page header) -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">Bölge Yönetimi</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="<?php echo e(route('dashboard')); ?>">Ana Sayfa</a></li>
                    <li class="breadcrumb-item active">Bölge Yönetimi</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title"><PERSON><PERSON><PERSON><PERSON></h3>
                        <div class="card-tools">
                            <a href="<?php echo e(route('regions.create')); ?>" class="btn btn-primary btn-sm">
                                <i class="fas fa-plus"></i> Yeni Bölge Ekle
                            </a>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table id="regionsTable" class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Bölge Adı</th>
                                        <th>Açıklama</th>
                                        <th>Bayi Sayısı</th>
                                        <th>Durum</th>
                                        <th>Oluşturulma Tarihi</th>
                                        <th>İşlemler</th>
                                    </tr>
                                </thead>
                                <tfoot>
                                    <tr>
                                        <th><input type="text" class="form-control form-control-sm" placeholder="ID Ara"></th>
                                        <th><input type="text" class="form-control form-control-sm" placeholder="Bölge Adı Ara"></th>
                                        <th><input type="text" class="form-control form-control-sm" placeholder="Açıklama Ara"></th>
                                        <th><input type="text" class="form-control form-control-sm" placeholder="Bayi Sayısı"></th>
                                        <th>
                                            <select class="form-control form-control-sm">
                                                <option value="">Tümü</option>
                                                <option value="aktif">Aktif</option>
                                                <option value="pasif">Pasif</option>
                                            </select>
                                        </th>
                                        <th></th>
                                        <th></th>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<!-- DataTables -->
<link rel="stylesheet" href="<?php echo e(asset('plugins/datatables-bs4/css/dataTables.bootstrap4.min.css')); ?>">
<link rel="stylesheet" href="<?php echo e(asset('plugins/datatables-responsive/css/responsive.bootstrap4.min.css')); ?>">
<link rel="stylesheet" href="<?php echo e(asset('plugins/datatables-buttons/css/buttons.bootstrap4.min.css')); ?>">
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<!-- DataTables  & Plugins -->
<script src="<?php echo e(asset('plugins/datatables/jquery.dataTables.min.js')); ?>"></script>
<script src="<?php echo e(asset('plugins/datatables-bs4/js/dataTables.bootstrap4.min.js')); ?>"></script>
<script src="<?php echo e(asset('plugins/datatables-responsive/js/dataTables.responsive.min.js')); ?>"></script>
<script src="<?php echo e(asset('plugins/datatables-responsive/js/responsive.bootstrap4.min.js')); ?>"></script>
<script src="<?php echo e(asset('plugins/datatables-buttons/js/dataTables.buttons.min.js')); ?>"></script>
<script src="<?php echo e(asset('plugins/datatables-buttons/js/buttons.bootstrap4.min.js')); ?>"></script>

<script>
$(document).ready(function() {
    var table = $('#regionsTable').DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: '<?php echo e(route("regions.datatable")); ?>',
            type: 'GET'
        },
        columns: [
            { data: 0, name: 'id', searchable: true },
            { data: 1, name: 'name', searchable: true },
            { data: 2, name: 'description', searchable: true },
            { data: 3, name: 'dealers_count', searchable: true, orderable: false },
            { data: 4, name: 'status', searchable: true },
            { data: 5, name: 'created_at', searchable: false },
            { data: 6, name: 'actions', searchable: false, orderable: false }
        ],
        order: [[0, 'desc']],
        pageLength: 25,
        lengthMenu: [[10, 25, 50, 100, -1], [10, 25, 50, 100, "Tümü"]],
        language: {
            url: '//cdn.datatables.net/plug-ins/1.13.7/i18n/tr.json'
        },
        responsive: true,
        autoWidth: false,
        initComplete: function () {
            // Apply the search
            this.api().columns().every(function (index) {
                var that = this;
                var input = $('tfoot th').eq(index).find('input, select');

                if (input.length) {
                    input.on('keyup change clear', function () {
                        if (that.search() !== this.value) {
                            that.search(this.value).draw();
                        }
                    });
                }
            });
        }
    });
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.index', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /var/www/crm.umram.online/resources/views/regions/index.blade.php ENDPATH**/ ?>