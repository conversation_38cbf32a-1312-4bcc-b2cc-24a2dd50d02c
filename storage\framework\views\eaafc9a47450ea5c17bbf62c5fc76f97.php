<?php $__env->startSection('title', 'Bayi <PERSON>netimi'); ?>

<?php $__env->startSection('content'); ?>
<!-- Content Header (Page header) -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">Bayi Yönetimi</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="<?php echo e(route('dashboard')); ?>">Ana Sayfa</a></li>
                    <li class="breadcrumb-item active">Bayi Yönetimi</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <a href="<?php echo e(route('dealers.create')); ?>" class="btn btn-primary">Yeni Bayi Ekle</a>
                        </h3>

                        <div class="card-tools">
                            <?php if(session('success')): ?>
                                <div class="alert alert-success">
                                    <?php echo e(session('success')); ?>

                                </div>
                            <?php endif; ?>

                            <?php if(session('error')): ?>
                                <div class="alert alert-danger">
                                    <?php echo e(session('error')); ?>

                                </div>
                            <?php endif; ?>

                            <form method="GET" action="" class="d-flex">
                                <div class="input-group input-group-sm">
                                    <input type="text" name="q" class="form-control float-right" placeholder="Bayi adı, şehir ara..." value="<?php echo e(request('q')); ?>">
                                    <div class="input-group-append">
                                        <button type="submit" class="btn btn-default">
                                            <i class="fas fa-search"></i>
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                    <!-- /.card-header -->
                    <div class="card-body table-responsive p-0">
                        <table class="table table-hover text-nowrap">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Bayi Adı</th>
                                    <th>Bölge</th>
                                    <th>İletişim Kişisi</th>
                                    <th>Telefon</th>
                                    <th>Şehir/İlçe</th>
                                    <th>Müşteri Sayısı</th>
                                    <th>Durum</th>
                                    <th>İşlemler</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__empty_1 = true; $__currentLoopData = $dealers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $dealer): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                    <tr>
                                        <td><?php echo e($dealer->id); ?></td>
                                        <td><?php echo e($dealer->name); ?></td>
                                        <td><?php echo e($dealer->region->name ?? '-'); ?></td>
                                        <td><?php echo e($dealer->contact_person ?? '-'); ?></td>
                                        <td><?php echo e($dealer->phone ?? '-'); ?></td>
                                        <td><?php echo e($dealer->city); ?> / <?php echo e($dealer->district); ?></td>
                                        <td><?php echo e($dealer->customers_count ?? $dealer->customers->count()); ?></td>
                                        <td>
                                            <?php if($dealer->status): ?>
                                                <span class="badge badge-success">Aktif</span>
                                            <?php else: ?>
                                                <span class="badge badge-danger">Pasif</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <a href="<?php echo e(route('dealers.show', $dealer->id)); ?>" class="btn btn-info btn-sm" title="Detay">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="<?php echo e(route('dealers.edit', $dealer->id)); ?>" class="btn btn-warning btn-sm" title="Düzenle">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <form action="<?php echo e(route('dealers.destroy', $dealer->id)); ?>" method="POST" style="display: inline-block;" onsubmit="return confirm('Bu bayiyi silmek istediğinizden emin misiniz?')">
                                                <?php echo csrf_field(); ?>
                                                <?php echo method_field('DELETE'); ?>
                                                <button type="submit" class="btn btn-danger btn-sm" title="Sil">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                    <tr>
                                        <td colspan="9" class="text-center">Henüz bayi eklenmemiş.</td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                    <!-- /.card-body -->
                    <div class="card-footer clearfix">
                        <?php echo e($dealers->links()); ?>

                    </div>
                </div>
                <!-- /.card -->
            </div>
        </div>
    </div>
</section>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.index', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /var/www/crm.umram.online/resources/views/dealers/index.blade.php ENDPATH**/ ?>